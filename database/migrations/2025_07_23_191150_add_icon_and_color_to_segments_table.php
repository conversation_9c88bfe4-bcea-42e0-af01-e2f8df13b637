<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('segments', function (Blueprint $table) {
            $table->string('icon')->nullable()->after('description');
            $table->string('color', 7)->nullable()->after('icon');
            $table->boolean('active')->default(true)->after('color');

            $table->index(['active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('segments', function (Blueprint $table) {
            $table->dropColumn(['icon', 'color', 'active']);
            $table->dropIndex(['active']);
        });
    }
};
