<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('segments', function (Blueprint $table) {
            // Dynamic segmentation fields
            $table->enum('segment_type', [
                'orders_count',
                'orders_sum',
                'orders_variety',
                'orders_time'
            ])->nullable()->after('percentage_goal')->comment('Type of dynamic segmentation criteria');

            $table->enum('comparison_operator', [
                '>', '<', '>=', '<=', '=', '!='
            ])->nullable()->after('segment_type')->comment('Comparison operator for dynamic segmentation');

            $table->decimal('comparison_value', 10, 2)->nullable()->after('comparison_operator')->comment('Threshold value for dynamic segmentation');

            $table->boolean('is_dynamic')->default(false)->after('comparison_value')->comment('Whether this segment uses dynamic criteria');

            // Add index for performance
            $table->index(['segment_type', 'comparison_operator']);
            $table->index(['is_dynamic']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('segments', function (Blueprint $table) {
            $table->dropIndex(['segment_type', 'comparison_operator']);
            $table->dropIndex(['is_dynamic']);
            $table->dropColumn([
                'segment_type',
                'comparison_operator',
                'comparison_value',
                'is_dynamic'
            ]);
        });
    }
};
