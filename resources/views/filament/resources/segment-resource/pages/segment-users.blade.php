<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Segment Info Card -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center space-x-4">
                @if($this->record->icon)
                    <div class="flex-shrink-0">
                        <x-heroicon-o-user-group class="w-8 h-8 text-primary-600" />
                    </div>
                @endif
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">{{ $this->record->title }}</h2>
                    @if($this->record->description)
                        <p class="text-gray-600 mt-1">{{ $this->record->description }}</p>
                    @endif
                    <div class="flex items-center space-x-4 mt-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ $this->record->getUserCountAttribute() }} users
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ $this->record->getUserPercentageAttribute() }}% of total users
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white rounded-lg shadow">
            {{ $this->table }}
        </div>
    </div>
</x-filament-panels::page>
