<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Segment extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'icon',
        'color',
        'active',
        'percentage_goal',
    ];

    protected $casts = [
        'active' => 'boolean',
        'percentage_goal' => 'integer',
    ];

    public function users()
    {
        return $this->belongsToMany(User::class, 'segment_user');
    }

    public function campaigns()
    {
        return $this->hasMany(Campaign::class);
    }

    public function getUserCountAttribute()
    {
        return $this->users()->count();
    }

    public function getUserPercentageAttribute()
    {
        $totalUsers = User::where('active', true)->count();
        if ($totalUsers === 0) {
            return 0;
        }
        return round(($this->getUserCountAttribute() / $totalUsers) * 100, 1);
    }

    public function getGoalStatusAttribute()
    {
        if (!$this->percentage_goal) {
            return 'no-goal';
        }

        $currentPercentage = $this->getUserPercentageAttribute();
        $goalPercentage = $this->percentage_goal;

        if ($currentPercentage >= $goalPercentage) {
            return 'achieved'; // Green
        } elseif ($currentPercentage >= ($goalPercentage * 0.7)) {
            return 'close'; // Orange
        } else {
            return 'needs-attention'; // Red
        }
    }

    public function getGoalStatusColorAttribute()
    {
        return match ($this->getGoalStatusAttribute()) {
            'achieved' => 'success',
            'close' => 'warning',
            'needs-attention' => 'danger',
            default => 'gray',
        };
    }

    public function getGoalStatusLabelAttribute()
    {
        return match ($this->getGoalStatusAttribute()) {
            'achieved' => 'Target Achieved',
            'close' => 'Close to Target',
            'needs-attention' => 'Needs Attention',
            default => 'No Goal Set',
        };
    }
}
