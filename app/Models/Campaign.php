<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Campaign extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'segment_id',
        'message_content',
        'subject',
        'sender_name',
        'sender_email',
        'push_title',
        'push_icon',
        'action_url',
        'status',
        'scheduled_at',
        'sent_at',
        'total_recipients',
        'delivered_count',
        'opened_count',
        'clicked_count',
        'failed_count',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'total_recipients' => 'integer',
        'delivered_count' => 'integer',
        'opened_count' => 'integer',
        'clicked_count' => 'integer',
        'failed_count' => 'integer',
    ];

    public function segment()
    {
        return $this->belongsTo(Segment::class);
    }

    public function getDeliveryRateAttribute()
    {
        if ($this->total_recipients === 0) {
            return 0;
        }
        return round(($this->delivered_count / $this->total_recipients) * 100, 1);
    }

    public function getOpenRateAttribute()
    {
        if ($this->delivered_count === 0) {
            return 0;
        }
        return round(($this->opened_count / $this->delivered_count) * 100, 1);
    }

    public function getClickRateAttribute()
    {
        if ($this->delivered_count === 0) {
            return 0;
        }
        return round(($this->clicked_count / $this->delivered_count) * 100, 1);
    }
}
