<?php

namespace App\Filament\Resources\SegmentResource\Pages;

use App\Filament\Resources\SegmentResource;
use App\Models\Segment;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;

class SegmentUsers extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = SegmentResource::class;

    protected static string $view = 'filament.resources.segment-resource.pages.segment-users';

    public Segment $record;

    public function mount(Segment $record): void
    {
        $this->record = $record;
    }

    public function getTitle(): string
    {
        return "Users in {$this->record->title}";
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('export')
                ->label('Export Users')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action('exportUsers'),
            Actions\Action::make('back')
                ->label('Back to Segments')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url(SegmentResource::getUrl('index')),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->record->users()->getQuery())
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('mobile')
                    ->searchable(),
                Tables\Columns\TextColumn::make('country')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('city')
                    ->searchable(),
                Tables\Columns\IconColumn::make('active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('country')
                    ->options([
                        'Kuwait' => 'Kuwait',
                        'Saudi Arabia' => 'Saudi Arabia',
                        'UAE' => 'UAE',
                        'Qatar' => 'Qatar',
                    ]),
                Tables\Filters\TernaryFilter::make('active')
                    ->boolean()
                    ->trueLabel('Active users')
                    ->falseLabel('Inactive users'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name');
    }

    public function exportUsers()
    {
        $users = $this->record->users;
        $filename = "segment_{$this->record->id}_users_" . now()->format('Y-m-d_H-i-s') . ".csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Name', 'Email', 'Mobile', 'Country', 'City', 'Gender', 'Active', 'Created At']);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->name,
                    $user->email,
                    $user->mobile,
                    $user->country,
                    $user->city,
                    $user->gender,
                    $user->active ? 'Yes' : 'No',
                    $user->created_at?->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
