/* Sala Marketing Portal - Colorful Entertainment Theme */

/* Main Body Background */
body {
    background: linear-gradient(135deg, #ffffff 0%, #4facfe 100%) !important;
    background-size: 400% 400% !important;
    animation: gradientShift 15s ease infinite !important;
    min-height: 100vh !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Main Content Area */
.fi-main {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 20px !important;
    margin: 20px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(10px) !important;
}

/* Page Background */
.fi-simple-layout, .fi-layout {
    background: transparent !important;
}

/* Navigation Enhancements - Bright and Usable */
.fi-sidebar {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%) !important;
    border-right: 2px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
}

.fi-sidebar-nav-item-label {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.fi-sidebar-nav-item:hover .fi-sidebar-nav-item-label {
    color: #ffffff !important;
    transform: translateX(4px) !important;
    transition: all 0.2s ease !important;
}

.fi-sidebar-nav-item-icon {
    color: #ffffff !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) !important;
}

.fi-sidebar-nav-item:hover .fi-sidebar-nav-item-icon {
    color: #ffffff !important;
    transform: scale(1.1) !important;
    transition: all 0.2s ease !important;
}

/* Active navigation item */
.fi-sidebar-nav-item.fi-active {
    background: rgba(255, 255, 255, 0.25) !important;
    border-radius: 12px !important;
    margin: 2px 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Header Enhancements - Vibrant Rainbow */
.fi-topbar {
    background: linear-gradient(90deg, #FFFFFF 0%, #feca57 25%, #48dbfb 50%, #ff9ff3 75%, #54a0ff 100%) !important;
    border-bottom: none !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

.fi-topbar-item {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Card Enhancements - Colorful Gradients */
.fi-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%) !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    border: 2px solid transparent !important;
    background-clip: padding-box !important;
    position: relative !important;
}

.fi-section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    border-radius: 16px !important;
    padding: 2px !important;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57) !important;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0) !important;
    mask-composite: exclude !important;
    z-index: -1 !important;
}

/* Widget Enhancements - Vibrant Stats */
.fi-wi-stats-overview-stat {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%) !important;
    border-radius: 16px !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3) !important;
    transition: all 0.3s ease !important;
    color: white !important;
}

.fi-wi-stats-overview-stat:nth-child(2) {
    background: linear-gradient(135deg, #4ecdc4 0%, #45b7d1 100%) !important;
    box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3) !important;
}

.fi-wi-stats-overview-stat:nth-child(3) {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%) !important;
    box-shadow: 0 8px 25px rgba(254, 202, 87, 0.3) !important;
}

.fi-wi-stats-overview-stat:nth-child(4) {
    background: linear-gradient(135deg, #54a0ff 0%, #5f27cd 100%) !important;
    box-shadow: 0 8px 25px rgba(84, 160, 255, 0.3) !important;
}

.fi-wi-stats-overview-stat:hover {
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important;
}

.fi-wi-stats-overview-stat .fi-wi-stats-overview-stat-value {
    color: white !important;
    font-weight: 800 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.fi-wi-stats-overview-stat .fi-wi-stats-overview-stat-description {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500 !important;
}

/* Button Enhancements - Vibrant and Fun */
.fi-btn-primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    font-weight: 700 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.fi-btn-primary:hover {
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6) !important;
    background: linear-gradient(135deg, #ff5252 0%, #26d0ce 50%, #2196f3 100%) !important;
}

.fi-btn-success {
    background: linear-gradient(135deg, #4ecdc4 0%, #44bd32 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4) !important;
    font-weight: 700 !important;
}

.fi-btn-success:hover {
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(78, 205, 196, 0.6) !important;
}

.fi-btn-warning {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(254, 202, 87, 0.4) !important;
    font-weight: 700 !important;
}

.fi-btn-warning:hover {
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(254, 202, 87, 0.6) !important;
}

.fi-btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4) !important;
    font-weight: 700 !important;
}

.fi-btn-danger:hover {
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6) !important;
}

/* Badge Enhancements - Bright and Playful */
.fi-badge {
    border-radius: 12px !important;
    font-weight: 700 !important;
    font-size: 0.75rem !important;
    padding: 0.4rem 1rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.2s ease !important;
}

.fi-badge:hover {
    transform: translateY(-1px) scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.fi-badge-color-success {
    background: linear-gradient(135deg, #4ecdc4 0%, #44bd32 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4) !important;
}

.fi-badge-color-warning {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(254, 202, 87, 0.4) !important;
}

.fi-badge-color-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4) !important;
}

.fi-badge-color-info {
    background: linear-gradient(135deg, #45b7d1 0%, #96ceb4 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(69, 183, 209, 0.4) !important;
}

.fi-badge-color-primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4) !important;
}

.fi-badge-color-gray {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4) !important;
}

/* Table Enhancements */
.fi-ta-table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.fi-ta-header-cell {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    font-weight: 600 !important;
    color: #374151 !important;
}

.fi-ta-row:hover {
    background: rgba(59, 130, 246, 0.02) !important;
}

/* Form Enhancements */
.fi-input {
    border-radius: 8px !important;
    border: 2px solid #e5e7eb !important;
    transition: all 0.2s ease !important;
}

.fi-input:focus {
    border-color: #3B82F6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.fi-select {
    border-radius: 8px !important;
    border: 2px solid #e5e7eb !important;
}

/* Page Header Enhancements */
.fi-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%) !important;
    border-radius: 12px !important;
    margin-bottom: 1.5rem !important;
    padding: 1.5rem !important;
}

.fi-header-heading {
    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 700 !important;
}

/* Chart Enhancements */
.fi-wi-chart {
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Grid Layout Enhancements */
.fi-ta-content-grid {
    gap: 1.5rem !important;
}

.fi-ta-content-grid-item {
    background: white !important;
    border-radius: 12px !important;
    border: 1px solid rgba(229, 231, 235, 0.5) !important;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.2s ease !important;
    overflow: hidden !important;
}

.fi-ta-content-grid-item:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1) !important;
}

/* Notification Enhancements */
.fi-no {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Loading Spinner */
.fi-loading-indicator {
    color: #3B82F6 !important;
}

/* Custom animations */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
}

.animate-pulse-glow {
    animation: pulse-glow 2s infinite;
}

/* Fun Animations and Effects */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0,-8px,0); }
    70% { transform: translate3d(0,-4px,0); }
    90% { transform: translate3d(0,-2px,0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes rainbow {
    0% { filter: hue-rotate(0deg); }
    100% { filter: hue-rotate(360deg); }
}

/* Logo Animation */
.fi-logo img {
    animation: pulse 3s ease-in-out infinite !important;
}

/* Navigation Group Headers */
.fi-sidebar-nav-group-label {
    color: #ffffff !important;
    font-weight: 800 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    background: rgba(255, 255, 255, 0.1) !important;
    padding: 8px 16px !important;
    border-radius: 8px !important;
    margin: 8px !important;
}

/* Table Row Hover Effects */
.fi-ta-row:hover {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%) !important;
    transform: translateX(4px) !important;
    transition: all 0.2s ease !important;
}

/* Form Field Focus Effects */
.fi-input:focus {
    border-color: #ff6b6b !important;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2), 0 0 20px rgba(255, 107, 107, 0.3) !important;
    transform: scale(1.02) !important;
    transition: all 0.2s ease !important;
}

/* Grid Item Hover Effects */
.fi-ta-content-grid-item:hover {
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%) !important;
}

/* Loading Spinner */
.fi-loading-indicator {
    color: #ff6b6b !important;
    animation: rainbow 2s linear infinite !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fi-sidebar {
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%) !important;
    }

    .fi-topbar {
        background: linear-gradient(90deg, #ff6b6b 0%, #feca57 25%, #48dbfb 50%, #ff9ff3 75%, #54a0ff 100%) !important;
    }

    .fi-main {
        margin: 10px !important;
        border-radius: 15px !important;
    }
}
