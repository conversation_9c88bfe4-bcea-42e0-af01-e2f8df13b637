/* Sala Marketing Portal - Colorful Entertainment Theme */

/* Navigation Enhancements - Bright and Usable */
.fi-sidebar {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%) !important;
    border-right: 2px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
}

.fi-sidebar-nav-item-label {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.fi-sidebar-nav-item:hover .fi-sidebar-nav-item-label {
    color: #ffffff !important;
    transform: translateX(4px) !important;
    transition: all 0.2s ease !important;
}

.fi-sidebar-nav-item-icon {
    color: #ffffff !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) !important;
}

.fi-sidebar-nav-item:hover .fi-sidebar-nav-item-icon {
    color: #ffffff !important;
    transform: scale(1.1) !important;
    transition: all 0.2s ease !important;
}

/* Active navigation item */
.fi-sidebar-nav-item.fi-active {
    background: rgba(255, 255, 255, 0.25) !important;
    border-radius: 12px !important;
    margin: 2px 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Header Enhancements - Vibrant Rainbow */
.fi-topbar {
    background: linear-gradient(90deg, #ff6b6b 0%, #feca57 25%, #48dbfb 50%, #ff9ff3 75%, #54a0ff 100%) !important;
    border-bottom: none !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

.fi-topbar-item {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Card Enhancements */
.fi-section {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    border: 1px solid rgba(229, 231, 235, 0.5) !important;
}

/* Widget Enhancements */
.fi-wi-stats-overview-stat {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(229, 231, 235, 0.5) !important;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.2s ease !important;
}

.fi-wi-stats-overview-stat:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1) !important;
}

/* Button Enhancements */
.fi-btn-primary {
    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    transition: all 0.2s ease !important;
}

.fi-btn-primary:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
}

.fi-btn-success {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    border: none !important;
    border-radius: 8px !important;
}

.fi-btn-warning {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%) !important;
    border: none !important;
    border-radius: 8px !important;
}

.fi-btn-danger {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
    border: none !important;
    border-radius: 8px !important;
}

/* Badge Enhancements */
.fi-badge {
    border-radius: 6px !important;
    font-weight: 600 !important;
    font-size: 0.75rem !important;
    padding: 0.25rem 0.75rem !important;
}

.fi-badge-color-success {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
    color: white !important;
}

.fi-badge-color-warning {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%) !important;
    color: white !important;
}

.fi-badge-color-danger {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
    color: white !important;
}

.fi-badge-color-info {
    background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%) !important;
    color: white !important;
}

.fi-badge-color-primary {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%) !important;
    color: white !important;
}

/* Table Enhancements */
.fi-ta-table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.fi-ta-header-cell {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    font-weight: 600 !important;
    color: #374151 !important;
}

.fi-ta-row:hover {
    background: rgba(59, 130, 246, 0.02) !important;
}

/* Form Enhancements */
.fi-input {
    border-radius: 8px !important;
    border: 2px solid #e5e7eb !important;
    transition: all 0.2s ease !important;
}

.fi-input:focus {
    border-color: #3B82F6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.fi-select {
    border-radius: 8px !important;
    border: 2px solid #e5e7eb !important;
}

/* Page Header Enhancements */
.fi-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%) !important;
    border-radius: 12px !important;
    margin-bottom: 1.5rem !important;
    padding: 1.5rem !important;
}

.fi-header-heading {
    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 700 !important;
}

/* Chart Enhancements */
.fi-wi-chart {
    border-radius: 12px !important;
    background: white !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Grid Layout Enhancements */
.fi-ta-content-grid {
    gap: 1.5rem !important;
}

.fi-ta-content-grid-item {
    background: white !important;
    border-radius: 12px !important;
    border: 1px solid rgba(229, 231, 235, 0.5) !important;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.2s ease !important;
    overflow: hidden !important;
}

.fi-ta-content-grid-item:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1) !important;
}

/* Notification Enhancements */
.fi-no {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Loading Spinner */
.fi-loading-indicator {
    color: #3B82F6 !important;
}

/* Custom animations */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
}

.animate-pulse-glow {
    animation: pulse-glow 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fi-sidebar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }
    
    .fi-topbar {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
    }
}
