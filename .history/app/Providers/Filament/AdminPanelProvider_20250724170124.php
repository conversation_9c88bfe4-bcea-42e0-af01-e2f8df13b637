<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;

use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->loginRouteSlug('login')
            ->authGuard('web')
            ->brandName('Sala Marketing Portal')
            ->brandLogo(asset('logo.png'))
            ->brandLogoHeight('2rem')
            ->favicon(asset('logo.png'))
            ->renderHook(
                'panels::head.end',
                fn (): string => '<style>' . file_get_contents(resource_path('css/filament-theme.css')) . '</style>'
            )

            ->colors([
                'primary' => Color::hex('#90C73E'), // Sala Green
                'secondary' => Color::hex('#4ecdc4'), // Turquoise
                'success' => Color::hex('#04AB9C'), // Bright Green
                'warning' => Color::hex('#FECE04'), // Sunny Yellow
                'danger' => Color::hex('#EF5022'), // Bright Red
                'info' => Color::hex('#00ACEE'), // Sky Blue
                'gray' => Color::hex('#74b9ff'), // Light Blue
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                \App\Filament\Widgets\SegmentsOverviewWidget::class,
                \App\Filament\Widgets\CampaignPerformanceChart::class,
                \App\Filament\Widgets\UserDistributionChart::class,
            ])
            ->authGuard('admin')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
