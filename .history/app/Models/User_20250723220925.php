<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class User extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'password',
        'first_name',
        'last_name',
        'mobile',
        'date_of_birth',
        'country',
        'city',
        'nationality',
        'gender',
        'lang',
        'active',
        'profile_photo_url',
        'referral_code',
        'referral_count',
        'is_test_account',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'date_of_birth' => 'date',
        'active' => 'boolean',
        'is_test_account' => 'boolean',
        'referral_count' => 'integer',
    ];

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function segments()
    {
        return $this->belongsToMany(Segment::class, 'segment_user')
            ->withTimestamps()
            ->withPivot('assigned_at');
    }
}
