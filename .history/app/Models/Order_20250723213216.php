<?php

namespace App\Models\Ordering;

use App\Helpers\Constant;
use App\Helpers\General;
use App\Models\Offer\Offer;
use App\Scopes\NonDraftScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Order extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $with = ['user', 'brand', 'branch', 'claimed_user', 'tickets', 'bundles'];

    protected $appends = [
        'no_tickets',
        'no_addons',
        'created_date',
        'payment_method_span',
        'payment_method_text',
        'payment_method_title',
        'payment_status_span',
        'status_span',
        'status_text',
        'day',
        'qr_code_status_span',
        'date',
        'booking_date',
        'claimed_date',
        'formate_date',
        'food_arrival_title',
        'corporate_info',
        'payment_status_text',
        'updated',
        'items',
        'canClaim',
        'order_branch_ids',
        'invoice_url',
        'invoice_qr_code',
    ];

    protected $fillable = [
        'user_id',
        'name',
        'mobile',
        'email',
        'order_number',
        'order_date',
        'expire_date',
        'corporate_request',
        'is_bulk',
        'customer_type',
        'qr_code',
        'notes',
        'status',
        'order_uuid',
        'payment_status',
        'qr_code_status',
        'payment_method',
        'brand_id',
        'branch_id',
        'price',
        'total_price',
        'vat',
        'discount',
        'fort_id',
        'signature',
        'access_code',
        'fort_status',
        'order_description',
        'merchant_reference',
        'customer_address1',
        'customer_address2',
        'customer_apartment_no',
        'customer_city',
        'customer_state',
        'customer_zip_code',
        'customer_country_code',
        'claimed_at',
        'claimed_user_id',
        'foodics_id',
        'coupon_id',
        'old_coupon_id',
        'coupon_discount',
        'parent',
        'is_parent',
        'is_refunded',
        'refund_amount',
        'order_time',
        'food_arrival',
        'digital_wallet',
        'reviewed',
        'reviewed_at',
        'device_uuid',
        'coupon_type',
        'governorate_id',
        'app_version',
        'card_digits',
        'is_draft',
        'sending_status',
        'is_parent_order_transfer',
        'parent_order_transfer',
        'referral_code',
        'referral_user_id',
        'offer_id',
        'parent_offer_order_id',
        'metadata',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope(new NonDraftScope());
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                'user.id',
                'name',
                'mobile',
                'email',
                'order_number',
                'order_date',
                'expire_date',
                'corporate_request',
                'is_bulk',
                'customer_type',
                'qr_code',
                'notes',
                'status',
                'payment_status',
                'qr_code_status',
                'payment_method',
                'brand.id',
                'branch.id',
                'price',
                'total_price',
                'vat',
                'discount',
                'fort_id',
                'signature',
                'access_code',
                'fort_status',
                'order_description',
                'merchant_reference',
                'customer_address1',
                'customer_address2',
                'customer_apartment_no',
                'customer_city',
                'customer_state',
                'customer_zip_code',
                'customer_country_code',
                'claimed_at',
                'claimed_user_id',
                'foodics_id',
                'coupon_id',
                'old_coupon_id',
                'coupon_discount',
                'is_refunded',
                'refund_amount',
                'order_time',
                'food_arrival',
                'digital_wallet',
                'coupon_type',
                'governorate_id',
                'app_version',
                'card_digits',
                'referral_code',
                'referral_user_id',
                'sending_status',
                'is_parent_order_transfer',
                'parent_order_transfer',
                'offer_id',
                'parent_offer_order_id',
                'metadata',
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function getFormateDateAttribute()
    {
        return date('Y/n/j', strtotime($this->order_date));
    }

    public function governorate()
    {
        return $this->belongsTo(\App\Models\Governorate\Governorate::class);
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Brand\Brand::class, 'brand_id')->withTrashed();
    }

    public function corporate(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Corporate\Corporate::class, 'corporate_request');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Brand\Branch::class, 'branch_id');
    }

    public function addons(): HasMany
    {
        return $this->hasMany(Addon\OrderAddon::class);
    }

    public function tickets()
    {
        return $this->hasMany(Ticket\OrderTicket::class)->with([
            'ticket',
            'orderTicketExtraInfo',
            'orderBulkTickets',
        ]);
    }

    public function bundles()
    {
        return $this->hasMany(Bundle\OrderBundle::class)->with(['bundle']);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Promotion\Coupon\Coupon::class);
    }

    public function parentObj()
    {
        return $this->belongsTo(self::class, 'parent');
    }

    public function parent()
    {
        return $this->belongsTo(self::class, 'parent');
    }

    public function children()
    {
        return $this->hasMany(self::class, 'parent');
    }

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    // referral_user
    public function referral_user()
    {
        return $this->belongsTo(\App\Models\User::class, 'referral_user_id');
    }

    public function claimed_user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'claimed_user_id');
    }

    public function getClaimedDateAttribute()
    {
        return $this->claimed_at != null ? date('M d, Y @ h:i a', strtotime($this->claimed_at)) : '';
    }

    public function scopeReviewed($query)
    {
        return $query->where('reviewed', 1);
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('order_date', 'desc');
    }

    public function getCreatedDateAttribute()
    {
        return $this->created_at != null ? date('M d, Y @ h:i a', strtotime($this->created_at)) : '';
    }

    public function getDateAttribute()
    {
        $value = $this->created_at;

        return $value != null ? date('m/d/Y', strtotime($value)) : '';
    }

    public function getUpdatedAttribute()
    {
        return $this->updated_at != null ? date('M d, Y @ h:i a', strtotime($this->updated_at)) : '';
    }

    public function getBookingDateAttribute()
    {
        return $this->order_date != null ? date('m/d/Y', strtotime($this->order_date)) : '';
    }

    public function getExpireDateAttribute($value)
    {
        return $value != null ? date('m/d/Y', strtotime($value)) : '';
    }

    public function getDayAttribute()
    {
        return $this->order_date != '' ? date('l', strtotime($this->order_date)) : '';
    }

    public function getNoTicketsAttribute()
    {
        return $this->tickets->sum('quantity');
    }

    public function getNoAddonsAttribute()
    {
        return $this->addons->sum('quantity');
    }

    public function getItemsAttribute()
    {
        $tickets = '';

        // $i = 1;
        // foreach ($this->tickets as $item) {
        //     $tickets = $tickets . $item->title_en . ' (' . $item->quantity . ')';
        //     if ($i < count($this->tickets)) {
        //         $tickets = $tickets . PHP_EOL;
        //     }
        //     $i++;
        // }
        // $i = 1;
        // foreach ($this->addons as $addon) {
        //     $tickets = $tickets . $addon->title_en . ' (' . $addon->quantity . ')';
        //     if ($i < count($this->tickets)) {
        //         $tickets = $tickets . PHP_EOL;
        //     }
        //     $i++;
        // }
        return $tickets;
    }

    public function scopePaid($query)
    {
        return $query->where('payment_status', '!=', 0);
    }

    public function getPaymentStatusSpanAttribute()
    {
        if ($this->payment_status == 0) {
            return "<span class='badge badge-pill badge-warning'>".__('StatusWaiting').'</span>';
        } elseif ($this->payment_status == 1) {
            return "<span class='badge badge-pill badge-success'>".__('StatusPaid').'</span>';
        } elseif ($this->payment_status == 2) {
            return "<span class='badge badge-pill badge-danger'>".__('StatusFailed').'</span>';
        } elseif ($this->payment_status == 3) {
            return "<span class='badge badge-pill badge-info'>".__('StatusRefunded').'</span>';
        } elseif ($this->payment_status == 4) {
            return "<span class='badge badge-pill  request-badge'>".__('StatusRequestRefund').'</span>';
        } elseif ($this->payment_status == 5) {
            return "<span class='badge badge-pill  badge-danger'> ".__('StatusCancelled').'</span>';
        } elseif ($this->payment_status == 6) {
            return "<span class='badge badge-pill  badge-danger'> ".__('StatusProcessing').'</span>';
        }
    }

    public function getPaymentMethodSpanAttribute()
    {
        if ($this->payment_method == 1) {
            return "<span class='badge badge-pill badge-warning'>".__('Cash').'</span>';
        } elseif ($this->payment_method == 2) {
            return "<span class='badge badge-pill badge-info'>".__('Coupon').'</span>';
        } elseif ($this->payment_method == 3) {
            return "<span class='badge badge-pill badge-success'>".__('PayFort').'</span>';
        } elseif ($this->payment_method == 4) {
            return "<span class='badge badge-pill badge-primary'> ".__('BankTransfer').'</span>';
        } elseif ($this->payment_method == 5) {
            return "<span class='badge badge-pill badge-primary'> ".__('OrderChild').'</span>';
        }
    }

    public function getPaymentStatusTextAttribute()
    {
        if ($this->payment_status == 0) {
            return 'Waiting';
        } elseif ($this->payment_status == 1) {
            return 'Paid';
        } elseif ($this->payment_status == 2) {
            return 'Failed';
        } elseif ($this->payment_status == 3) {
            return 'Refunded';
        } elseif ($this->payment_status == 4) {
            return 'Request Refund';
        } elseif ($this->payment_status == 5) {
            return 'Cancelled';
        }
    }

    public function getPaymentMethodTextAttribute()
    {
        if (count($this->payments()) > 0) {
            $value = '';
            foreach ($this->payments() as $payment) {
                $value .= $value != '' ? ' - ' : '';
                if ($payment->method_type == '1') {
                    $value .= __('payfort');
                } elseif ($payment->method_type == '2') {
                    $value .= __('tamara');
                } else {
                    $value .= $payment->method_id == 1 ? ' Sala credit' : ' Loyalty';
                }
            }

            return $value;
        } else {
            if ($this->payment_method == 1) {
                return 'Cash';
            } elseif ($this->payment_method == 2) {
                return 'Coupon';
            } elseif ($this->payment_method == 3) {
                return 'PayFort';
            } elseif ($this->payment_method == 4) {
                return 'Bank Transfer';
            } elseif ($this->payment_method == 5) {
                return 'Child Order';
            } elseif ($this->payment_method == 7) {
                return 'Tamara';
            }
        }
    }

    public function getStatusTextAttribute()
    {
        if ($this->status == 0) {
            return 'Coming';
        } elseif ($this->status == 1) {
            return 'Used';
        } elseif ($this->status == 2) {
            return 'cancelled';
        } elseif ($this->status == 3) {
            return "Didn't show";
        } elseif ($this->status == 4) {
            return 'Partially Used';
        }
    }

    public function getFoodArrivalTitleAttribute()
    {
        $general = new General();
        $item = $general->getConstantItemById('FoodArrival', $this->food_arrival);

        return $item ? $item['title_en'] : '';
    }

    public function getPaymentMethodTitleAttribute()
    {
        $general = new General();
        $item = $general->getConstantItemById('PaymentMethod', $this->payment_method);

        return $item ? $item['title_en'] : '';
    }

    public function getStatusSpanAttribute()
    {
        if ($this->status == 0) {
            return "<span class='badge badge-pill badge-warning'>".__('Upcoming').'</span>';
        } elseif ($this->status == 1) {
            return "<span class='badge badge-pill badge-success'>".__('Used').'</span>';
        } elseif ($this->status == 2) {
            return "<span class='badge badge-pill badge-danger'>".__('Cancelled').'</span>';
        } elseif ($this->status == 3) {
            return "<span class='badge badge-pill badge-info'> ".__('DoesNotShow').'</span>';
        } elseif ($this->status == 4) {
            return "<span class='badge badge-pill badge-secondary'> ".__('PartiallyUsed').'</span>';
        }
    }

    public function getQrCodeStatusSpanAttribute()
    {
        if ($this->qr_code_status == 0) {
            return "<span class='badge badge-pill badge-warning'>Pending</span>";
        } elseif ($this->qr_code_status == 1) {
            return "<span class='badge badge-pill badge-success'>Used</span>";
        } elseif ($this->qr_code_status == 2) {
            return "<span class='badge badge-pill badge-danger'>Expired</span>";
        }
    }

    public function getCorporateInfoAttribute()
    {
        if ($this->is_bulk) {
            return DB::table('corporates')
                ->select('id', 'name', 'mobile', 'email')
                ->where('id', $this->corporate_request)
                ->first();
        }
    }

    // OrderBranch
    public function getOrderBranchIdsAttribute()
    {
        if ($this->is_bulk) {
            return DB::table('order_branches')
                ->where('order_id', $this->id)
                ->pluck('branch_id')
                ->toArray();
        }

        return [];
    }

    public function getPriceAttrAttribute()
    {
        $price = $this->price;
        if (!$this->parent_offer_order_id && $this->offer_id) {
            $price = 0;
            foreach ($this->offer_sub_orders as $subOrder) {
                foreach ($subOrder->tickets as $ticket) {
                    $price += $ticket->total_price;
                }
            }
        }

        return $price;
    }

    public function getCouponDiscountAttribute($coupon_discount)
    {
        if (!$this->parent_offer_order_id && $this->offer_id) {
            $coupon_discount = $this->total_price - $this->price_attr;
        }

        return $coupon_discount;
    }

    public function getNetPriceAttribute()
    {
        return round($this->price_attr / 1.15);
    }

    public function getVatValueAttribute()
    {
        return $this->price_attr - $this->net_price;
    }

    public function getOrderNetPriceAttribute()
    {
        return round($this->total_price / 1.15, 1);
    }

    public function getOrderVatValueAttribute()
    {
        return round($this->total_price - $this->order_net_price, 1);
    }

    public function getVatAttribute()
    {
        return Constant::VAT;
    }

    public function getNextId()
    {
        $statement = DB::select("show table status like 'orders'");

        return $statement[0]->Auto_increment;
    }

    public function getCanClaimAttribute()
    {
        date_default_timezone_set('Asia/Riyadh');
        $today = date('Y-m-d H:i:s');
        $tomorrowAt2Am = date('Y-m-d H:i:s', strtotime('tomorrow 02:00:00'));
        $date = $this->order_date;
        $time = date('H:i:s');
        $orderDateTime = date('Y-m-d', strtotime($date)).' '.$time;
        $canClaim = false;

        if (isset($this->order_time) && $this->order_time != '') {
            $orderDateTime = date('Y-m-d', strtotime($date)).' '.$this->order_time;
        }
        if ($orderDateTime <= $tomorrowAt2Am
            && (date('Y-m-d') == date('Y-m-d', strtotime($date))
                || date('Y-m-d', strtotime('tomorrow 02:00:00')) == date('Y-m-d', strtotime($date)))
        ) {
            $canClaim = true;
        }

        // if type ticket is normal cant claim if order branch not in admin roles permission
        // get admin branches
        $branch_ids = [];
        if (
            \Cookie::get('role_brands')
            && $this->is_bulk != 1
            && !auth()
                ->user()
                ->hasRole('super-admin')
        ) {
            $brand_ids = unserialize(\Cookie::get('role_brands'));
            $ids = Auth()
                ->user()
                ->roles->pluck('id')
                ->toArray();
            $branch_ids = \App\Models\Permission\RoleBranch::whereIn('role_id', $ids)
                ->pluck('branch_id')
                ->toArray();
            if (!in_array($this->branch_id, $branch_ids)) {
                $canClaim = false;
            }

            return $canClaim;
        }

        return $canClaim;
    }

    public function getInvoiceUrlAttribute()
    {
        $orderUuid = $this->order_uuid;
        $locale = app()->getLocale();

        $locale = app()->getLocale();

        return url($locale.'/generate-invoice/'.$orderUuid);
    }

    public function getInvoiceQrCodeAttribute()
    {
        $invoice = ZatcaInvoice::where('order_id', $this->id)
            ->where('reportingStatus', 'REPORTED')
            ->first();

        return !empty($invoice) && $invoice->qrCode ? $invoice->qrCode : '';
    }

    public function scopeDraft($query)
    {
        return $query->where('is_draft', '1');
    }

    public function scopeIsOffer($query)
    {
        return $query->whereNotNull('offer_id')->orWhereNotNull('parent_offer_order_id');
    }

    public function scopeIsNotOffer($query)
    {
        return $query->whereNull('offer_id')->whereNull('parent_offer_order_id');
    }

    public function getStatusTitleAttribute()
    {
        $general = new General();
        $item = $general->getConstantItemById('OrderStatus', $this->status);

        return $item ? $item['title_en'] : '';
    }

    // get order payment fn
    public function payments()
    {
        return Payment::select('method_type', 'method_id', 'amount', 'transaction_id', 'is_refund')
            ->with('transaction')

            ->where('order_id', $this->id)
            ->NotRefunded()
            ->distinct(['method_type', 'method_id'])
            ->get();
    }

    public function refunded_payments()
    {
        return Payment::with('transaction')
            ->where('order_id', $this->id)
            ->distinct(['method_type', 'method_id'])
            ->Refunded()

            ->get();
    }

    public function parentOrderTransferObj()
    {
        return $this->belongsTo(Order::class, 'parent_order_transfer');
    }

    public function offer(): BelongsTo
    {
        return $this->belongsTo(Offer::class);
    }

    public function parentOfferOrder(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'parent_offer_order_id', 'id')->withoutGlobalScope(NonDraftScope::class);
    }

    public function offer_sub_orders(): HasMany
    {
        return $this->hasMany(Order::class, 'parent_offer_order_id', 'id');
    }

    public function offer_parent_order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'parent_offer_order_id', 'id');
    }

    public function draft_offer_sub_orders(): HasMany
    {
        return $this->hasMany(Order::class, 'parent_offer_order_id', 'id')->withoutGlobalScope(NonDraftScope::class);
    }

    public function invoices()
    {
        return $this->hasMany(ZatcaInvoice::class, 'order_id');
    }

    // payWithExternal
    public function getPayWithExternalAttribute()
    {
        return Payment::where('order_id', $this->id)
            ->NotRefunded()
            ->External()
            ->first()->amount ?? '';
    }

    // pay Loyality
    public function getPayWithLoyalityAttribute()
    {
        return Payment::where('order_id', $this->id)
            ->NotRefunded()
            ->Loyalty()
            ->sum('amount');
    }

    // pay SalaCredit
    public function getPayWithSalaCreditAttribute()
    {
        return Payment::where('order_id', $this->id)
            ->NotRefunded()
            ->Wallet()
            ->sum('amount');
    }

    public function getRefundPaymentTextAttribute()
    {
        $value = '';
        if (count($this->payments()) > 0) {
            foreach ($this->refunded_payments() as $payment) {
                $value .= $value != '' ? ' - ' : '';
                if ($payment->method_type == '1') {
                    $value .= __('payfort').': '.$payment->amount.' @ '.$payment->date.', ';
                } elseif ($payment->method_type == '2') {
                    $value .= __('tamara').': '.$payment->amount.' @ '.$payment->date.', ';
                } else {
                    $value .=
                        $payment->method_id == 1
                            ? ' Sala credit: ('.$payment->amount.') @ '.$payment->date.', '
                            : ' Loyalty: ('.$payment->amount.') @ '.$payment->date.', ';
                }
            }
        } else {
            $value .= __('payfort').' '.$this->total_price;
        }

        return $value;
    }

    public function getNameAttribute($value)
    {
        if ($this->is_bulk) {
            return $this->corporate->name ?? '';
        }

        return $value;
    }

    public function getEmailAttribute($value)
    {
        if ($this->is_bulk) {
            return $this->corporate->email ?? '';
        }

        return $value;
    }

    public function getMobileAttribute($value)
    {
        if ($this->is_bulk) {
            return $this->corporate->mobile ?? '';
        }

        return $value;
    }
}
