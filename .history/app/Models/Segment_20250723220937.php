<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Segment extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'icon',
        'color',
        'active',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    public function users()
    {
        return $this->belongsToMany(User::class, 'segment_user')
            ->withTimestamps()
            ->withPivot('assigned_at');
    }

    public function campaigns()
    {
        return $this->hasMany(Campaign::class);
    }

    public function getUserCountAttribute()
    {
        return $this->users()->count();
    }

    public function getUserPercentageAttribute()
    {
        $totalUsers = User::where('active', true)->count();
        if ($totalUsers === 0) {
            return 0;
        }
        return round(($this->user_count / $totalUsers) * 100, 1);
    }
}
