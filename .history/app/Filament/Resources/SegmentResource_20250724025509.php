<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SegmentResource\Pages;
use App\Filament\Resources\SegmentResource\RelationManagers;
use App\Models\Segment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\Layout\Grid;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ColorColumn;
use Filament\Tables\Actions\Action;
use Filament\Support\Enums\FontWeight;

class SegmentResource extends Resource
{
    protected static ?string $model = Segment::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = 'Segments';

    protected static ?string $modelLabel = 'Segment';

    protected static ?string $pluralModelLabel = 'Segments';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('icon')
                    ->label('Icon (Heroicon name)')
                    ->placeholder('heroicon-o-star')
                    ->maxLength(255),
                Forms\Components\ColorPicker::make('color')
                    ->label('Color'),
                Forms\Components\TextInput::make('percentage_goal')
                    ->label('Target Percentage (%)')
                    ->numeric()
                    ->minValue(1)
                    ->maxValue(100)
                    ->helperText('Target percentage of total users for this segment'),
                Forms\Components\Toggle::make('active')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->contentGrid([
                'md' => 2,
                'lg' => 3,
                'xl' => 4,
            ])
            ->columns([
                Stack::make([
                    // Goal Status Badge
                    TextColumn::make('goal_status_label')
                        ->label('Status')
                        ->formatStateUsing(fn ($state, $record) => $record->getGoalStatusLabelAttribute())
                        ->badge()
                        ->color(fn ($record) => $record->getGoalStatusColorAttribute()),

                    Stack::make([
                        TextColumn::make('title')
                            ->weight(FontWeight::Bold)
                            ->size(TextColumn\TextColumnSize::Large)
                            ->color(fn ($record) => $record->getGoalStatusColorAttribute()),
                        TextColumn::make('description')
                            ->color('gray')
                            ->limit(50),
                    ])->space(1),

                    Stack::make([
                        TextColumn::make('user_count')
                            ->label('Users')
                            ->formatStateUsing(fn ($state, $record) => $record->getUserCountAttribute() . ' users')
                            ->badge()
                            ->color('success'),
                        TextColumn::make('user_percentage')
                            ->label('Current')
                            ->formatStateUsing(fn ($state, $record) => $record->getUserPercentageAttribute() . '%')
                            ->badge()
                            ->color('info'),
                        TextColumn::make('percentage_goal')
                            ->label('Target')
                            ->formatStateUsing(fn ($state) => $state ? $state . '%' : 'No target')
                            ->badge()
                            ->color('warning'),
                    ])->space(1),
                ])->space(3),
            ])
            ->filters([
               
            ])
            ->actions([
                Action::make('view_users')
                    ->label('View Users')
                    ->icon('heroicon-o-users')
                    ->color('primary')
                    ->url(fn (Segment $record): string => route('filament.admin.resources.segments.users', $record))
                    ->openUrlInNewTab(false),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('title')->defaultPaginationPageOption(25);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSegments::route('/'),
            'create' => Pages\CreateSegment::route('/create'),
            'edit' => Pages\EditSegment::route('/{record}/edit'),
            'users' => Pages\SegmentUsers::route('/{record}/users'),
        ];
    }
}
