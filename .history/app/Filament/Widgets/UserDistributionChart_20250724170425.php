<?php

namespace App\Filament\Widgets;

use App\Models\User;
use Filament\Widgets\ChartWidget;

class UserDistributionChart extends ChartWidget
{
    protected static ?string $heading = 'User Distribution by Country';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $usersByCountry = User::where('active', true)
            ->groupBy('country')
            ->selectRaw('country, COUNT(*) as count')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        $countries = [];
        $counts = [];
        $colors = [
            'rgba(144, 199, 62, 0.8)', 'rgba(4, 171, 156, 0.8)', 'rgba(254, 206, 4, 0.8)',
            'rgba(239, 80, 34, 0.8)', 'rgba(0, 172, 238, 0.8)', 'rgba(116, 185, 255, 0.8)',
            'rgba(78, 205, 196, 0.8)', 'rgba(255, 159, 243, 0.8)', 'rgba(132, 204, 22, 0.8)',
            'rgba(99, 102, 241, 0.8)'
        ];

        foreach ($usersByCountry as $data) {
            $countries[] = $data->country ?: 'Unknown';
            $counts[] = $data->count;
        }

        return [
            'datasets' => [
                [
                    'data' => $counts,
                    'backgroundColor' => array_slice($colors, 0, count($counts)),
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $countries,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
