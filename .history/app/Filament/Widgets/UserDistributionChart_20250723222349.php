<?php

namespace App\Filament\Widgets;

use App\Models\User;
use Filament\Widgets\ChartWidget;

class UserDistribution<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = 'User Distribution by Country';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $usersByCountry = User::where('active', true)
            ->groupBy('country')
            ->selectRaw('country, COUNT(*) as count')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        $countries = [];
        $counts = [];
        $colors = [
            '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
            '#EC4899', '#14B8A6', '#F97316', '#84CC16', '#6366F1'
        ];

        foreach ($usersByCountry as $data) {
            $countries[] = $data->country ?: 'Unknown';
            $counts[] = $data->count;
        }

        return [
            'datasets' => [
                [
                    'data' => $counts,
                    'backgroundColor' => array_slice($colors, 0, count($counts)),
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $countries,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
