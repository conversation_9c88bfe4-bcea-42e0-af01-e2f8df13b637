<?php

namespace App\Filament\Widgets;

use App\Models\Campaign;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class CampaignPerformanceChart extends ChartWidget
{
    protected static ?string $heading = 'Campaign Performance by Type';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $campaignStats = Campaign::select('type')
            ->selectRaw('COUNT(*) as total')
            ->selectRaw('SUM(total_recipients) as recipients')
            ->selectRaw('SUM(delivered_count) as delivered')
            ->selectRaw('SUM(opened_count) as opened')
            ->selectRaw('SUM(clicked_count) as clicked')
            ->groupBy('type')
            ->get();

        $types = [];
        $totalCampaigns = [];
        $deliveryRates = [];
        $openRates = [];
        $clickRates = [];

        foreach ($campaignStats as $stat) {
            $types[] = ucfirst($stat->type);
            $totalCampaigns[] = $stat->total;

            // Calculate rates
            $deliveryRate = $stat->recipients > 0 ? ($stat->delivered / $stat->recipients) * 100 : 0;
            $openRate = $stat->delivered > 0 ? ($stat->opened / $stat->delivered) * 100 : 0;
            $clickRate = $stat->delivered > 0 ? ($stat->clicked / $stat->delivered) * 100 : 0;

            $deliveryRates[] = round($deliveryRate, 1);
            $openRates[] = round($openRate, 1);
            $clickRates[] = round($clickRate, 1);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Total Campaigns',
                    'data' => $totalCampaigns,
                    'backgroundColor' => '#3B82F6',
                ],
                [
                    'label' => 'Delivery Rate (%)',
                    'data' => $deliveryRates,
                    'backgroundColor' => '#10B981',
                ],
                [
                    'label' => 'Open Rate (%)',
                    'data' => $openRates,
                    'backgroundColor' => '#F59E0B',
                ],
                [
                    'label' => 'Click Rate (%)',
                    'data' => $clickRates,
                    'backgroundColor' => '#EF4444',
                ],
            ],
            'labels' => $types,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
