<?php

namespace App\Filament\Widgets;

use App\Models\Segment;
use App\Models\User;
use App\Models\Campaign;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SegmentsOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalUsers = User::where('active', true)->count();
        $totalSegments = Segment::where('active', true)->count();
        $totalCampaigns = Campaign::count();
        $activeCampaigns = Campaign::whereIn('status', ['scheduled', 'sent'])->count();

        // Get the largest segment
        $largestSegment = Segment::where('active', true)
            ->withCount('users')
            ->orderBy('users_count', 'desc')
            ->first();

        return [
            Stat::make('Total Users', $totalUsers)
                ->description('Active users in the system')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('Active Segments', $totalSegments)
                ->description('User segments available')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('primary'),

            Stat::make('Total Campaigns', $totalCampaigns)
                ->description("{$activeCampaigns} active campaigns")
                ->descriptionIcon('heroicon-m-megaphone')
                ->color('warning'),

            Stat::make('Largest Segment', $largestSegment ? $largestSegment->users_count : 0)
                ->description($largestSegment ? $largestSegment->title : 'No segments')
                ->descriptionIcon('heroicon-m-star')
                ->color('info'),
        ];
    }
}
