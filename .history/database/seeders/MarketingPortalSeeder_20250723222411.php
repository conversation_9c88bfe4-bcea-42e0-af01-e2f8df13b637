<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Segment;
use App\Models\Campaign;
use Illuminate\Support\Facades\Hash;

class MarketingPortalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample users
        $users = [];
        for ($i = 1; $i <= 100; $i++) {
            $users[] = User::create([
                'name' => "User {$i}",
                'email' => "user{$i}@example.com",
                'first_name' => "First{$i}",
                'last_name' => "Last{$i}",
                'mobile' => "+96612345{$i}",
                'country' => ['Kuwait', 'Saudi Arabia', 'UAE', 'Qatar'][array_rand(['Kuwait', 'Saudi Arabia', 'UAE', 'Qatar'])],
                'city' => ['Kuwait City', 'Riyadh', 'Dubai', 'Doha'][array_rand(['Kuwait City', 'Riyadh', 'Dubai', 'Doha'])],
                'gender' => ['male', 'female'][array_rand(['male', 'female'])],
                'active' => true,
            ]);
        }

        // Create sample segments
        $segments = [
            [
                'title' => 'VIP Customers',
                'description' => 'High-value customers with premium status',
                'icon' => 'heroicon-o-star',
                'color' => '#FFD700',
                'active' => true,
            ],
            [
                'title' => 'New Users',
                'description' => 'Recently registered users',
                'icon' => 'heroicon-o-user-plus',
                'color' => '#10B981',
                'active' => true,
            ],
            [
                'title' => 'Frequent Buyers',
                'description' => 'Users who make regular purchases',
                'icon' => 'heroicon-o-shopping-cart',
                'color' => '#3B82F6',
                'active' => true,
            ],
            [
                'title' => 'Inactive Users',
                'description' => 'Users who haven\'t been active recently',
                'icon' => 'heroicon-o-clock',
                'color' => '#EF4444',
                'active' => true,
            ],
            [
                'title' => 'Kuwait Residents',
                'description' => 'Users located in Kuwait',
                'icon' => 'heroicon-o-map-pin',
                'color' => '#8B5CF6',
                'active' => true,
            ],
        ];

        foreach ($segments as $segmentData) {
            $segment = Segment::create($segmentData);

            // Assign random users to each segment
            $randomUsers = collect($users)->random(rand(10, 30));
            $segment->users()->attach($randomUsers->pluck('id'));
        }

        // Create sample campaigns
        $campaigns = [
            [
                'title' => 'Welcome SMS Campaign',
                'description' => 'Welcome message for new users',
                'type' => 'sms',
                'segment_id' => Segment::where('title', 'New Users')->first()->id,
                'message_content' => 'Welcome to our platform! Start exploring amazing features today.',
                'status' => 'sent',
                'total_recipients' => 25,
                'delivered_count' => 24,
                'opened_count' => 0, // SMS doesn't track opens
                'clicked_count' => 0,
                'sent_at' => now()->subDays(2),
            ],
            [
                'title' => 'VIP Exclusive Offers',
                'description' => 'Special offers for VIP customers',
                'type' => 'email',
                'segment_id' => Segment::where('title', 'VIP Customers')->first()->id,
                'message_content' => '<h1>Exclusive VIP Offers</h1><p>Check out our latest premium deals just for you!</p>',
                'subject' => 'Exclusive VIP Offers - Limited Time!',
                'sender_name' => 'Sala Marketing Team',
                'sender_email' => '<EMAIL>',
                'action_url' => 'https://example.com/vip-offers',
                'status' => 'sent',
                'total_recipients' => 18,
                'delivered_count' => 17,
                'opened_count' => 12,
                'clicked_count' => 8,
                'sent_at' => now()->subDays(1),
            ],
            [
                'title' => 'Flash Sale Alert',
                'description' => 'Push notification for flash sale',
                'type' => 'push',
                'segment_id' => Segment::where('title', 'Frequent Buyers')->first()->id,
                'message_content' => 'Flash Sale! 50% off on selected items. Limited time offer!',
                'push_title' => 'Flash Sale Alert 🔥',
                'push_icon' => 'https://example.com/sale-icon.png',
                'action_url' => 'https://example.com/flash-sale',
                'status' => 'sent',
                'total_recipients' => 22,
                'delivered_count' => 21,
                'opened_count' => 0, // Push notifications don't track opens the same way
                'clicked_count' => 15,
                'sent_at' => now()->subHours(6),
            ],
            [
                'title' => 'Re-engagement Campaign',
                'description' => 'Email to re-engage inactive users',
                'type' => 'email',
                'segment_id' => Segment::where('title', 'Inactive Users')->first()->id,
                'message_content' => '<h1>We Miss You!</h1><p>Come back and see what\'s new. Special discount waiting for you!</p>',
                'subject' => 'We Miss You - Come Back for 20% Off!',
                'sender_name' => 'Sala Team',
                'sender_email' => '<EMAIL>',
                'action_url' => 'https://example.com/comeback-offer',
                'status' => 'scheduled',
                'scheduled_at' => now()->addDays(1),
                'total_recipients' => 0,
                'delivered_count' => 0,
                'opened_count' => 0,
                'clicked_count' => 0,
            ],
        ];

        foreach ($campaigns as $campaignData) {
            Campaign::create($campaignData);
        }
    }
}
