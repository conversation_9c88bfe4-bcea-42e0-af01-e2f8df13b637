<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Segment;
use App\Models\Campaign;
use Illuminate\Support\Facades\Hash;

class MarketingPortalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample users
        $users = [];
        for ($i = 1; $i <= 100; $i++) {
            $users[] = User::create([
                'name' => "User {$i}",
                'email' => "user{$i}@example.com",
                'first_name' => "First{$i}",
                'last_name' => "Last{$i}",
                'mobile' => "+96612345{$i}",
                'country' => ['Kuwait', 'Saudi Arabia', 'UAE', 'Qatar'][array_rand(['Kuwait', 'Saudi Arabia', 'UAE', 'Qatar'])],
                'city' => ['Kuwait City', 'Riyadh', 'Dubai', 'Doha'][array_rand(['Kuwait City', 'Riyadh', 'Dubai', 'Doha'])],
                'gender' => ['male', 'female'][array_rand(['male', 'female'])],
                'active' => true,
            ]);
        }

        // Create sample segments
        $segments = [
            [
                'title' => 'VIP Customers',
                'description' => 'High-value customers with premium status',
                'icon' => 'heroicon-o-star',
                'color' => '#FFD700',
                'active' => true,
            ],
            [
                'title' => 'New Users',
                'description' => 'Recently registered users',
                'icon' => 'heroicon-o-user-plus',
                'color' => '#10B981',
                'active' => true,
            ],
            [
                'title' => 'Frequent Buyers',
                'description' => 'Users who make regular purchases',
                'icon' => 'heroicon-o-shopping-cart',
                'color' => '#3B82F6',
                'active' => true,
            ],
            [
                'title' => 'Inactive Users',
                'description' => 'Users who haven\'t been active recently',
                'icon' => 'heroicon-o-clock',
                'color' => '#EF4444',
                'active' => true,
            ],
            [
                'title' => 'Kuwait Residents',
                'description' => 'Users located in Kuwait',
                'icon' => 'heroicon-o-map-pin',
                'color' => '#8B5CF6',
                'active' => true,
            ],
        ];

        foreach ($segments as $segmentData) {
            $segment = Segment::create($segmentData);

            // Assign random users to each segment
            $randomUsers = collect($users)->random(rand(10, 30));
            $segment->users()->attach($randomUsers->pluck('id'));
        }
    }
}
