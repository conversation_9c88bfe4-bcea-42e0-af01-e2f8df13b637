<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['sms', 'email', 'push']);
            $table->foreignId('segment_id')->constrained()->onDelete('cascade');
            $table->text('message_content');

            // Type-specific fields
            $table->string('subject')->nullable(); // For email campaigns
            $table->string('sender_name')->nullable(); // For email campaigns
            $table->string('sender_email')->nullable(); // For email campaigns
            $table->string('push_title')->nullable(); // For push notifications
            $table->string('push_icon')->nullable(); // For push notifications
            $table->string('action_url')->nullable(); // For push notifications and emails

            // Campaign status and scheduling
            $table->enum('status', ['draft', 'scheduled', 'sent', 'failed'])->default('draft');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('sent_at')->nullable();

            // Analytics
            $table->integer('total_recipients')->default(0);
            $table->integer('delivered_count')->default(0);
            $table->integer('opened_count')->default(0);
            $table->integer('clicked_count')->default(0);
            $table->integer('failed_count')->default(0);

            $table->timestamps();

            $table->index(['type']);
            $table->index(['status']);
            $table->index(['segment_id']);
            $table->index(['scheduled_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaigns');
    }
};
